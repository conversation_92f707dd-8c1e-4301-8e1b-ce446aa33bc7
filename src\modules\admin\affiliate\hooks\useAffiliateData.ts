/**
 * Hooks cho dữ liệu affiliate
 */
import { useQuery, useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import {
  AffiliateAccountQueryDto,
  AffiliateOrderQueryDto,
  AffiliateRankQueryDto,
  CreateAffiliateRankDto,
  CreateAffiliateRankResponseDto,
  UpdateAffiliateRankDto,
} from '../types/api.types';
import { PublisherStatus, mapPublisherStatusToApiStatus } from '../types/affiliate.types';
import {
  affiliateAccountApi,
  affiliateRankApi,
  affiliateOrderApi,
} from '../services/affiliate-api.service';

// Định nghĩa các key cho React Query
const AFFILIATE_DATA_KEYS = {
  // Publisher
  publishers: (queryDto?: AffiliateAccountQueryDto) => ['affiliate', 'publishers', queryDto],
  publisher: (id: number) => ['affiliate', 'publisher', id],

  // Rank affiliate
  ranks: (queryDto?: AffiliateRankQueryDto) => ['affiliate', 'ranks', queryDto],
  rank: (id: number) => ['affiliate', 'rank', id],
  conditionRanges: () => ['affiliate', 'condition-ranges'],

  // Đơn hàng affiliate
  orders: (queryDto?: AffiliateOrderQueryDto) => ['affiliate', 'orders', queryDto],
  order: (id: number) => ['affiliate', 'order', id],
};

/**
 * Hook cho dữ liệu publisher
 */
export const usePublisherData = () => {
  const queryClient = useQueryClient();

  /**
   * Hook để lấy danh sách publisher
   * @param queryDto Tham số truy vấn
   * @returns Query object
   */
  const usePublishers = (queryDto?: AffiliateAccountQueryDto) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.publishers(queryDto),
      queryFn: async () => {
        const response = await affiliateAccountApi.getAccounts(queryDto || { page: 1, limit: 10 });
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để lấy thông tin publisher theo ID
   * @param id ID publisher
   * @returns Query object
   */
  const usePublisher = (id: number) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.publisher(id),
      queryFn: async () => {
        const response = await affiliateAccountApi.getAccountById(id);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để cập nhật trạng thái publisher
   * @returns Mutation object
   */
  const useUpdatePublisherStatus = () => {
    return useMutation({
      mutationFn: async ({ id, status }: { id: number; status: PublisherStatus }) => {
        const apiStatus = mapPublisherStatusToApiStatus(status);
        const response = await affiliateAccountApi.updateAccountStatus(id, apiStatus);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
      onSuccess: (data, variables) => {
        // Cập nhật cache cho chi tiết publisher
        queryClient.setQueryData(AFFILIATE_DATA_KEYS.publisher(variables.id), data);

        // Làm mới danh sách publisher
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'publishers'],
        });
      },
    });
  };

  return {
    usePublishers,
    usePublisher,
    useUpdatePublisherStatus,
  };
};

/**
 * Hook cho dữ liệu rank affiliate
 */
export const useAffiliateRankData = () => {
  const queryClient = useQueryClient();

  /**
   * Hook để lấy danh sách rank affiliate
   * @param queryDto Tham số truy vấn
   * @returns Query object
   */
  const useRanks = (queryDto?: AffiliateRankQueryDto) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.ranks(queryDto),
      queryFn: async () => {
        const response = await affiliateRankApi.getRanks(queryDto || { page: 1, limit: 10 });
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để lấy thông tin rank affiliate theo ID
   * @param id ID rank affiliate
   * @returns Query object
   */
  const useRank = (id: number) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.rank(id),
      queryFn: async () => {
        const response = await affiliateRankApi.getRankById(id);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để tạo rank affiliate mới
   * @returns Mutation object
   */
  const useCreateRank = () => {
    return useMutation({
      mutationFn: async (createRankDto: CreateAffiliateRankDto) => {
        const response = await affiliateRankApi.createRank(createRankDto);
        return response as CreateAffiliateRankResponseDto; // Trả về dữ liệu từ API với type cụ thể
      },
      onSuccess: () => {
        // Làm mới danh sách rank
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'ranks'],
        });
      },
    });
  };

  /**
   * Hook để cập nhật thông tin rank affiliate
   * @returns Mutation object
   */
  const useUpdateRank = () => {
    return useMutation({
      mutationFn: async ({ id, data }: { id: number; data: UpdateAffiliateRankDto }) => {
        const response = await affiliateRankApi.updateRank(id, data);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
      onSuccess: (data, variables: { id: number; data: UpdateAffiliateRankDto }) => {
        // Cập nhật cache cho chi tiết rank
        queryClient.setQueryData(AFFILIATE_DATA_KEYS.rank(variables.id), data);

        // Làm mới danh sách rank
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'ranks'],
        });
      },
    });
  };

  /**
   * Hook để cập nhật trạng thái kích hoạt của rank affiliate
   * @returns Mutation object
   */
  const useUpdateRankStatus = () => {
    return useMutation({
      mutationFn: async ({ id, isActive }: { id: number; isActive: boolean }) => {
        const response = await affiliateRankApi.updateRankStatus(id, isActive);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
      onSuccess: (data, variables: { id: number; isActive: boolean }) => {
        // Cập nhật cache cho chi tiết rank
        queryClient.setQueryData(AFFILIATE_DATA_KEYS.rank(variables.id), data);

        // Làm mới danh sách rank
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'ranks'],
        });
      },
    });
  };

  /**
   * Hook để lấy khoảng minCondition và maxCondition đã sử dụng
   * @returns Query object
   */
  const useUsedConditionRanges = () => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.conditionRanges(),
      queryFn: async () => {
        const response = await affiliateRankApi.getUsedConditionRanges();
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để xóa rank affiliate
   * @returns Mutation object
   */
  const useDeleteRank = () => {
    return useMutation({
      mutationFn: async (id: number) => {
        const response = await affiliateRankApi.deleteRank(id);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
      onSuccess: () => {
        // Làm mới danh sách rank
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'ranks'],
        });
      },
    });
  };

  /**
   * Hook để xóa nhiều rank affiliate cùng lúc
   * @returns Mutation object
   */
  const useBulkDeleteRanks = () => {
    return useMutation({
      mutationFn: async (ids: number[]) => {
        const response = await affiliateRankApi.bulkDeleteRanks(ids);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
      onSuccess: () => {
        // Làm mới danh sách rank
        queryClient.invalidateQueries({
          queryKey: ['affiliate', 'ranks'],
        });
      },
    });
  };

  return {
    useRanks,
    useRank,
    useCreateRank,
    useUpdateRank,
    useUpdateRankStatus,
    useUsedConditionRanges,
    useDeleteRank,
    useBulkDeleteRanks,
  };
};

/**
 * Hook cho dữ liệu đơn hàng affiliate
 */
export const useAffiliateOrderData = () => {
  /**
   * Hook để lấy danh sách đơn hàng affiliate
   * @param queryDto Tham số truy vấn
   * @returns Query object
   */
  const useOrders = (queryDto?: AffiliateOrderQueryDto) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.orders(queryDto),
      queryFn: async () => {
        const response = await affiliateOrderApi.getOrders(queryDto || { page: 1, limit: 10 });
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  /**
   * Hook để lấy thông tin đơn hàng affiliate theo ID
   * @param id ID đơn hàng affiliate
   * @returns Query object
   */
  const useOrder = (id: number) => {
    return useQuery({
      queryKey: AFFILIATE_DATA_KEYS.order(id),
      queryFn: async () => {
        const response = await affiliateOrderApi.getOrderById(id);
        return response; // Trả về trực tiếp dữ liệu từ API
      },
    });
  };

  return {
    useOrders,
    useOrder,
  };
};
